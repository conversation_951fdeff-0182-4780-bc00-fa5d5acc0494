package org.jeecg.modules.occu.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.vo.ZyRiskFactorPage;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.jeecg.modules.occu.service.IZyRiskFactorItemgroupService;
import org.jeecg.modules.occu.mapper.ZyRiskFactorWorktypeMapper;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.basicinfo.util.PinyinUtil;
import org.jeecg.modules.basicinfo.util.WubiUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date: 2025-02-18
 * @Version: V1.0
 */
@Api(tags = "危害因素")
@RestController
@RequestMapping("/occu/zyRiskFactor")
@Slf4j
public class ZyRiskFactorController {
    @Autowired
    private IZyRiskFactorService zyRiskFactorService;
    @Autowired
    private IZyRiskFactorItemgroupService zyRiskFactorItemgroupService;
    @Autowired
    private ZyRiskFactorWorktypeMapper zyRiskFactorWorktypeMapper;
    @Autowired
    private ICustomerRegService customerRegService;

    /**
     * 分页列表查询
     *
     * @param zyRiskFactor
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "危害因素-分页列表查询")
    @ApiOperation(value = "危害因素-分页列表查询", notes = "危害因素-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ZyRiskFactor>> queryPageList(ZyRiskFactor zyRiskFactor, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyRiskFactor> queryWrapper = QueryGenerator.initQueryWrapper(zyRiskFactor, req.getParameterMap());
        Page<ZyRiskFactor> page = new Page<ZyRiskFactor>(pageNo, pageSize);
        IPage<ZyRiskFactor> pageList = zyRiskFactorService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyRiskFactorPage
     * @return
     */
    @AutoLog(value = "危害因素-添加")
    @ApiOperation(value = "危害因素-添加", notes = "危害因素-添加")
//    @RequiresPermissions("occu:zy_risk_factor:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ZyRiskFactorPage zyRiskFactorPage) {
        ZyRiskFactor zyRiskFactor = new ZyRiskFactor();
        BeanUtils.copyProperties(zyRiskFactorPage, zyRiskFactor);
        // 使用带工种关联的保存（若列表为空内部会忽略）
        zyRiskFactorService.saveMain(zyRiskFactor, zyRiskFactorPage.getZyRiskFactorItemgroupList(), zyRiskFactorPage.getZyRiskFactorWorktypeList());
        // 清除危害因素缓存
        customerRegService.clearRiskFactorCache();
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyRiskFactorPage
     * @return
     */
    @AutoLog(value = "危害因素-编辑")
    @ApiOperation(value = "危害因素-编辑", notes = "危害因素-编辑")
//    @RequiresPermissions("occu:zy_risk_factor:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ZyRiskFactorPage zyRiskFactorPage) {
        ZyRiskFactor zyRiskFactor = new ZyRiskFactor();
        BeanUtils.copyProperties(zyRiskFactorPage, zyRiskFactor);
        ZyRiskFactor zyRiskFactorEntity = zyRiskFactorService.getById(zyRiskFactor.getId());
        if (zyRiskFactorEntity == null) {
            return Result.error("未找到对应数据");
        }
        zyRiskFactorService.updateMain(zyRiskFactor, zyRiskFactorPage.getZyRiskFactorItemgroupList(), zyRiskFactorPage.getZyRiskFactorWorktypeList());
        // 清除危害因素缓存
        customerRegService.clearRiskFactorCache();
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "危害因素-通过id删除")
    @ApiOperation(value = "危害因素-通过id删除", notes = "危害因素-通过id删除")
//    @RequiresPermissions("occu:zy_risk_factor:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        zyRiskFactorService.delMain(id);
        // 清除危害因素缓存
        customerRegService.clearRiskFactorCache();
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "危害因素-批量删除")
    @ApiOperation(value = "危害因素-批量删除", notes = "危害因素-批量删除")
//    @RequiresPermissions("occu:zy_risk_factor:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zyRiskFactorService.delBatchMain(Arrays.asList(ids.split(",")));
        // 清除危害因素缓存
        customerRegService.clearRiskFactorCache();
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "危害因素-通过id查询")
    @ApiOperation(value = "危害因素-通过id查询", notes = "危害因素-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ZyRiskFactor> queryById(@RequestParam(name = "id", required = true) String id) {
        ZyRiskFactor zyRiskFactor = zyRiskFactorService.getById(id);
        if (zyRiskFactor == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(zyRiskFactor);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "危害因素必检项目-通过主表ID查询")
    @ApiOperation(value = "危害因素必检项目-通过主表ID查询", notes = "危害因素必检项目-通过主表ID查询")
    @GetMapping(value = "/queryZyRiskFactorItemgroupByMainId")
    public Result<IPage<ZyRiskFactorItemgroup>> queryZyRiskFactorItemgroupListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList = zyRiskFactorItemgroupService.selectByMainId(id);
        IPage<ZyRiskFactorItemgroup> page = new Page<>();
        page.setRecords(zyRiskFactorItemgroupList);
        page.setTotal(zyRiskFactorItemgroupList.size());
        return Result.OK(page);
    }

    /**
     * 通过危害因素ID查询工种关联数据
     *
     * @param id 危害因素ID
     * @return
     */
    @ApiOperation(value = "危害因素工种关联-通过主表ID查询", notes = "危害因素工种关联-通过主表ID查询")
    @GetMapping(value = "/queryZyRiskFactorWorktypeByMainId")
    public Result<IPage<ZyRiskFactorWorktype>> queryZyRiskFactorWorktypeListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList = zyRiskFactorWorktypeMapper.selectByMainId(id);
        IPage<ZyRiskFactorWorktype> page = new Page<>();
        page.setRecords(zyRiskFactorWorktypeList);
        page.setTotal(zyRiskFactorWorktypeList.size());
        return Result.OK(page);
    }

    /**
     * 一键补充助记码和五笔简码
     *
     * @return
     */
    @AutoLog(value = "危害因素-一键补充助记码和五笔简码")
    @ApiOperation(value = "危害因素-一键补充助记码和五笔简码", notes = "危害因素-一键补充助记码和五笔简码")
    @PostMapping(value = "/batchUpdateHelpCharAndWubiCode")
    public Result<String> batchUpdateHelpCharAndWubiCode() {
        try {
            // 查询所有危害因素
            List<ZyRiskFactor> allFactors = zyRiskFactorService.list();
            int updateCount = 0;

            for (ZyRiskFactor factor : allFactors) {
                boolean needUpdate = false;

                // 如果名称不为空
                if (StringUtils.isNotBlank(factor.getName())) {
                    // 如果助记码为空或需要更新，生成拼音首字母
                    if (StringUtils.isBlank(factor.getHelpChar())) {
                        factor.setHelpChar(PinyinUtil.generateSmartHelpChar(factor.getName()));
                        needUpdate = true;
                    }

                    // 如果五笔简码为空或需要更新，生成五笔简码
                    if (StringUtils.isBlank(factor.getWubiCode())) {
                        factor.setWubiCode(WubiUtil.generateSmartWubiCode(factor.getName()));
                        needUpdate = true;
                    }
                }

                // 如果需要更新，则保存
                if (needUpdate) {
                    zyRiskFactorService.updateById(factor);
                    updateCount++;
                }
            }

            // 清除危害因素缓存
            customerRegService.clearRiskFactorCache();

            return Result.OK("成功补充 " + updateCount + " 条记录的助记码和五笔简码！");
        } catch (Exception e) {
            log.error("一键补充助记码和五笔简码失败", e);
            return Result.error("补充失败：" + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param zyRiskFactor
     */
    @RequiresPermissions("occu:zy_risk_factor:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyRiskFactor zyRiskFactor) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<ZyRiskFactor> queryWrapper = QueryGenerator.initQueryWrapper(zyRiskFactor, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<ZyRiskFactor> zyRiskFactorList = zyRiskFactorService.list(queryWrapper);

        // Step.3 组装pageList
        List<ZyRiskFactorPage> pageList = new ArrayList<ZyRiskFactorPage>();
        for (ZyRiskFactor main : zyRiskFactorList) {
            ZyRiskFactorPage vo = new ZyRiskFactorPage();
            BeanUtils.copyProperties(main, vo);
            List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList = zyRiskFactorItemgroupService.selectByMainId(main.getId());
            vo.setZyRiskFactorItemgroupList(zyRiskFactorItemgroupList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "危害因素列表");
        mv.addObject(NormalExcelConstants.CLASS, ZyRiskFactorPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("危害因素数据", "导出人:" + sysUser.getRealname(), "危害因素"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("occu:zy_risk_factor:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyRiskFactorPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyRiskFactorPage.class, params);
                for (ZyRiskFactorPage page : list) {
                    ZyRiskFactor po = new ZyRiskFactor();
                    BeanUtils.copyProperties(page, po);
                    zyRiskFactorService.saveMain(po, page.getZyRiskFactorItemgroupList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 根据工种ID查询关联的危害因素列表
     *
     * @param worktypeId 工种ID
     * @return 危害因素列表
     */
    @AutoLog(value = "危害因素-根据工种查询")
    @ApiOperation(value = "危害因素-根据工种查询", notes = "危害因素-根据工种查询")
    @GetMapping(value = "/getRiskFactorsByWorktype")
    public Result<List<ZyRiskFactor>> getRiskFactorsByWorktype(@RequestParam(name = "worktypeId", required = true) String worktypeId) {
        List<ZyRiskFactor> riskFactors = zyRiskFactorService.getRiskFactorsByWorktype(worktypeId);
        return Result.OK(riskFactors);
    }

    /**
     * 根据工种ID查询关联的危害因素列表
     *
     * @param workTypeCode 工种代码
     * @return 危害因素列表
     */
    @AutoLog(value = "危害因素-根据工种查询")
    @ApiOperation(value = "危害因素-根据工种查询", notes = "危害因素-根据工种查询")
    @GetMapping(value = "/getRiskFactorsByWorktypeCode")
    public Result<List<ZyRiskFactor>> getRiskFactorsByWorktypeCode(@RequestParam(name = "workTypeCode", required = true) String workTypeCode) {
        List<ZyRiskFactor> riskFactors = zyRiskFactorService.getRiskFactorsByWorktypeCode(workTypeCode);
        return Result.OK(riskFactors);
    }


    /**
     * 根据危害因素ID查询关联的必检项目列表
     *
     * @param riskFactorId 危害因素ID
     * @return 必检项目列表
     */
    @AutoLog(value = "危害因素-根据危害因素查询必检项目")
    @ApiOperation(value = "危害因素-根据危害因素查询必检项目", notes = "危害因素-根据危害因素查询必检项目")
    @GetMapping(value = "/getItemGroupsByRiskFactor")
    public Result<List<ZyRiskFactorItemgroup>> getItemGroupsByRiskFactor(@RequestParam(name = "riskFactorId", required = true) String riskFactorId) {
        List<ZyRiskFactorItemgroup> itemGroups = zyRiskFactorItemgroupService.selectByMainId(riskFactorId);
        return Result.OK(itemGroups);
    }

    /**
     * 根据工种ID直接获取所有关联的必检项目列表（工种→危害因素→必检项目）
     *
     * @param worktypeId 工种ID
     * @param jobStatus  岗位类别
     * @return 必检项目列表
     */
    @AutoLog(value = "危害因素-根据工种和岗位类别查询必检项目")
    @ApiOperation(value = "危害因素-根据工种和岗位类别查询必检项目", notes = "危害因素-根据工种和岗位类别查询必检项目")
    @GetMapping(value = "/getItemGroupsByWorktype")
    public Result<List<ZyRiskFactorItemgroup>> getItemGroupsByWorktype(@RequestParam(name = "worktypeId", required = true) String worktypeId, @RequestParam(name = "jobStatus", required = false) String jobStatus) {

        // 1. 根据工种查询危害因素
        List<ZyRiskFactor> riskFactors = zyRiskFactorService.getRiskFactorsByWorktype(worktypeId);

        // 2. 根据危害因素查询必检项目
        List<ZyRiskFactorItemgroup> allItemGroups = new ArrayList<>();
        for (ZyRiskFactor riskFactor : riskFactors) {
            List<ZyRiskFactorItemgroup> itemGroups = zyRiskFactorItemgroupService.selectByMainId(riskFactor.getId());
            allItemGroups.addAll(itemGroups);
        }

        return Result.OK(allItemGroups);
    }

}
