package org.jeecg.modules.basicinfo.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @Description: 五笔编码工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
@Slf4j
public class WubiUtil {

    // 汉字到五笔编码的映射表
    private static final Map<Character, String> WUBI_MAP = new HashMap<>();

    // 是否已初始化
    private static volatile boolean initialized = false;

    static {
        initWubiMap();
    }

    /**
     * 获取中文字符串的五笔简码
     * @param chinese 中文字符串
     * @return 五笔简码大写字符串
     */
    public static String getWubiCode(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        // 确保已初始化
        ensureInitialized();

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (isChinese(c)) {
                // 中文字符
                String wubi = WUBI_MAP.get(c);
                if (wubi != null && wubi.length() > 0) {
                    // 取五笔编码的第一个字母作为简码
                    result.append(wubi.charAt(0));
                } else {
                    // 如果没有找到五笔编码，使用拼音首字母作为替代
                    String pinyin = PinyinUtil.getFirstChars(String.valueOf(c));
                    if (StringUtils.isNotBlank(pinyin)) {
                        result.append(pinyin);
                    } else {
                        // 如果拼音也获取不到，使用默认字符
                        result.append("Z");
                    }
                }
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toUpperCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 获取汉字的完整五笔编码
     * @param chinese 中文字符串
     * @return 完整五笔编码字符串
     */
    public static String getFullWubiCode(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        // 确保已初始化
        ensureInitialized();

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (isChinese(c)) {
                // 中文字符
                String wubi = WUBI_MAP.get(c);
                if (wubi != null) {
                    result.append(wubi).append(" ");
                } else {
                    // 如果没有找到五笔编码，使用拼音作为替代
                    String pinyin = PinyinUtil.getFullPinyin(String.valueOf(c));
                    if (StringUtils.isNotBlank(pinyin)) {
                        result.append(pinyin.toUpperCase()).append(" ");
                    } else {
                        result.append("ZZZZ ");
                    }
                }
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toUpperCase(c)).append(" ");
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c).append(" ");
            }
        }
        return result.toString().trim();
    }

    /**
     * 生成智能五笔简码
     * 优先使用五笔编码，如果没有中文则使用英文首字母
     */
    public static String generateSmartWubiCode(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (containsChinese(name)) {
            return getWubiCode(name);
        } else {
            // 英文或数字，直接提取首字母
            StringBuilder result = new StringBuilder();
            for (char c : name.toCharArray()) {
                if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                } else if (Character.isDigit(c)) {
                    result.append(c);
                }
            }
            return result.toString();
        }
    }

    /**
     * 检查字符是否是中文
     */
    private static boolean isChinese(char c) {
        return c >= 0x4e00 && c <= 0x9fa5;
    }

    /**
     * 检查字符串是否包含中文
     */
    public static boolean containsChinese(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        for (char c : str.toCharArray()) {
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 初始化五笔编码映射表
     * 从配置文件中加载完整的五笔编码表
     */
    private static void initWubiMap() {
        if (initialized) {
            return;
        }

        synchronized (WubiUtil.class) {
            if (initialized) {
                return;
            }

            try {
                loadWubiTable();
                initialized = true;
                log.info("五笔编码表初始化成功，共加载 {} 个汉字编码", WUBI_MAP.size());
            } catch (Exception e) {
                log.error("五笔编码表初始化失败", e);
                // 初始化失败时，添加一些基本的编码作为兜底
                initBasicWubiMap();
                initialized = true;
            }
        }
    }

    /**
     * 从配置文件加载五笔编码表
     */
    private static void loadWubiTable() throws IOException {
        Properties properties = new Properties();
        try (InputStream inputStream = WubiUtil.class.getClassLoader()
                .getResourceAsStream("wubi/wubi-table.properties")) {

            if (inputStream == null) {
                throw new IOException("找不到五笔编码表文件: wubi/wubi-table.properties");
            }

            properties.load(inputStream);

            // 将Properties中的数据转换为Map
            for (String key : properties.stringPropertyNames()) {
                if (key.length() == 1) {
                    char character = key.charAt(0);
                    String wubiCode = properties.getProperty(key);
                    if (StringUtils.isNotBlank(wubiCode)) {
                        WUBI_MAP.put(character, wubiCode.trim().toUpperCase());
                    }
                }
            }
        }
    }

    /**
     * 初始化基本的五笔编码映射（兜底方案）
     */
    private static void initBasicWubiMap() {
        // 一级简码
        WUBI_MAP.put('一', "GGLL");
        WUBI_MAP.put('地', "FBN");
        WUBI_MAP.put('在', "DHGN");
        WUBI_MAP.put('要', "SVF");
        WUBI_MAP.put('工', "AAA");
        WUBI_MAP.put('上', "HHGD");
        WUBI_MAP.put('是', "JGF");
        WUBI_MAP.put('中', "KHK");
        WUBI_MAP.put('国', "LGYI");
        WUBI_MAP.put('同', "MKG");
        WUBI_MAP.put('和', "TKD");
        WUBI_MAP.put('的', "RQYY");
        WUBI_MAP.put('有', "DEF");
        WUBI_MAP.put('人', "WW");
        WUBI_MAP.put('我', "TRNT");
        WUBI_MAP.put('主', "YGD");
        WUBI_MAP.put('产', "UTE");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('为', "YLWI");
        WUBI_MAP.put('这', "YPD");
        WUBI_MAP.put('民', "NAV");
        WUBI_MAP.put('了', "BNH");
        WUBI_MAP.put('发', "NTY");
        WUBI_MAP.put('以', "NYWY");
        WUBI_MAP.put('经', "XAG");

        log.warn("使用基本五笔编码表作为兜底方案，共 {} 个字符", WUBI_MAP.size());
    }

    /**
     * 确保五笔编码表已初始化
     */
    private static void ensureInitialized() {
        if (!initialized) {
            initWubiMap();
        }
    }

}
