package org.jeecg.modules.basicinfo.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 五笔编码工具类
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
@Slf4j
public class WubiUtil {

    // 常用汉字的五笔编码映射表（简化版本，实际项目中建议使用完整的五笔编码库）
    private static final Map<Character, String> WUBI_MAP = new HashMap<>();

    static {
        initWubiMap();
    }

    /**
     * 获取中文字符串的五笔简码
     * @param chinese 中文字符串
     * @return 五笔简码大写字符串
     */
    public static String getWubiCode(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                // 中文字符
                String wubi = WUBI_MAP.get(c);
                if (wubi != null) {
                    // 取五笔编码的第一个字母作为简码
                    result.append(wubi.charAt(0));
                } else {
                    // 如果没有找到五笔编码，使用拼音首字母作为替代
                    result.append(PinyinUtil.getFirstChars(String.valueOf(c)));
                }
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toUpperCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 生成智能五笔简码
     * 优先使用五笔编码，如果没有中文则使用英文首字母
     */
    public static String generateSmartWubiCode(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (PinyinUtil.containsChinese(name)) {
            return getWubiCode(name);
        } else {
            // 英文或数字，直接提取首字母
            StringBuilder result = new StringBuilder();
            for (char c : name.toCharArray()) {
                if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                } else if (Character.isDigit(c)) {
                    result.append(c);
                }
            }
            return result.toString();
        }
    }

    /**
     * 初始化五笔编码映射表（这里只是示例，实际应该使用完整的五笔编码库）
     */
    private static void initWubiMap() {
        // 常用汉字的五笔编码（示例）
        WUBI_MAP.put('电', "JNV");
        WUBI_MAP.put('工', "AAA");
        WUBI_MAP.put('焊', "OJNG");
        WUBI_MAP.put('司', "KCB");
        WUBI_MAP.put('机', "SMN");
        WUBI_MAP.put('操', "RJGH");
        WUBI_MAP.put('作', "WTHF");
        WUBI_MAP.put('维', "XWY");
        WUBI_MAP.put('修', "WH");
        WUBI_MAP.put('清', "IGE");
        WUBI_MAP.put('洁', "IGFH");
        WUBI_MAP.put('员', "KMU");
        WUBI_MAP.put('人', "WW");
        WUBI_MAP.put('管', "TPNN");
        WUBI_MAP.put('理', "GL");
        WUBI_MAP.put('技', "RAH");
        WUBI_MAP.put('术', "SY");
        WUBI_MAP.put('师', "JGF");
        WUBI_MAP.put('长', "TAY");
        WUBI_MAP.put('主', "YGD");
        WUBI_MAP.put('任', "TFK");
        WUBI_MAP.put('副', "BLH");
        WUBI_MAP.put('助', "BEG");
        WUBI_MAP.put('高', "YMKF");
        WUBI_MAP.put('级', "XGN");
        WUBI_MAP.put('中', "KHK");
        WUBI_MAP.put('初', "PUV");
        WUBI_MAP.put('危', "QDN");
        WUBI_MAP.put('害', "PDD");
        WUBI_MAP.put('因', "LDA");
        WUBI_MAP.put('素', "XFF");
        WUBI_MAP.put('噪', "KGKJ");
        WUBI_MAP.put('声', "FNR");
        WUBI_MAP.put('粉', "OWX");
        WUBI_MAP.put('尘', "IFF");
        WUBI_MAP.put('化', "WXN");
        WUBI_MAP.put('学', "IPB");
        WUBI_MAP.put('物', "TRN");
        WUBI_MAP.put('质', "RFM");
        WUBI_MAP.put('毒', "GEP");
        WUBI_MAP.put('有', "DEF");
        WUBI_MAP.put('害', "PDD");
        WUBI_MAP.put('气', "RNB");
        WUBI_MAP.put('体', "WSG");
        WUBI_MAP.put('高', "YMKF");
        WUBI_MAP.put('温', "JLG");
        WUBI_MAP.put('低', "WQY");
        WUBI_MAP.put('辐', "LXG");
        WUBI_MAP.put('射', "XTD");
        WUBI_MAP.put('振', "RDFH");
        WUBI_MAP.put('动', "FCL");
        WUBI_MAP.put('紫', "HXI");
        WUBI_MAP.put('外', "QHY");
        WUBI_MAP.put('线', "XGT");
        WUBI_MAP.put('红', "XAG");
        WUBI_MAP.put('激', "IGL");
        WUBI_MAP.put('光', "IQB");
        WUBI_MAP.put('微', "TMI");
        WUBI_MAP.put('波', "IHCY");
        WUBI_MAP.put('电', "JNV");
        WUBI_MAP.put('磁', "YXJN");
        WUBI_MAP.put('场', "FNR");
        WUBI_MAP.put('强', "JLXY");
        WUBI_MAP.put('度', "YACI");
        WUBI_MAP.put('照', "JVK");
        WUBI_MAP.put('明', "JEG");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('足', "KHU");
        WUBI_MAP.put('缺', "RMWY");
        WUBI_MAP.put('氧', "RNQY");
        WUBI_MAP.put('密', "NYNY");
        WUBI_MAP.put('闭', "UDD");
        WUBI_MAP.put('空', "PWB");
        WUBI_MAP.put('间', "UJD");
        WUBI_MAP.put('重', "TGF");
        WUBI_MAP.put('复', "TGMH");
        WUBI_MAP.put('性', "TLG");
        WUBI_MAP.put('劳', "APL");
        WUBI_MAP.put('强', "JLXY");
        WUBI_MAP.put('迫', "THPK");
        WUBI_MAP.put('体', "WSG");
        WUBI_MAP.put('位', "WUG");
        WUBI_MAP.put('异', "NAJ");
        WUBI_MAP.put('常', "IPKH");
        WUBI_MAP.put('生', "TGD");
        WUBI_MAP.put('物', "TRN");
        WUBI_MAP.put('病', "UTE");
        WUBI_MAP.put('原', "DRU");
        WUBI_MAP.put('细', "XTU");
        WUBI_MAP.put('菌', "AJU");
        WUBI_MAP.put('病', "UTE");
        WUBI_MAP.put('毒', "GEP");
        WUBI_MAP.put('真', "FHF");
        WUBI_MAP.put('菌', "AJU");
        WUBI_MAP.put('寄', "PDSK");
        WUBI_MAP.put('生', "TGD");
        WUBI_MAP.put('虫', "JHN");
        WUBI_MAP.put('其', "ADW");
        WUBI_MAP.put('他', "WN");
        WUBI_MAP.put('心', "NYU");
        WUBI_MAP.put('理', "GL");
        WUBI_MAP.put('社', "PYF");
        WUBI_MAP.put('会', "WFC");
        WUBI_MAP.put('环', "GAV");
        WUBI_MAP.put('境', "FJL");
        WUBI_MAP.put('压', "DGY");
        WUBI_MAP.put('力', "LT");
        WUBI_MAP.put('精', "OGH");
        WUBI_MAP.put('神', "SJH");
        WUBI_MAP.put('紧', "JCXI");
        WUBI_MAP.put('张', "XAH");
        WUBI_MAP.put('疲', "UPI");
        WUBI_MAP.put('劳', "APL");
        WUBI_MAP.put('单', "JGD");
        WUBI_MAP.put('调', "YKH");
        WUBI_MAP.put('枯', "SGF");
        WUBI_MAP.put('燥', "OAWO");
        WUBI_MAP.put('工', "AAA");
        WUBI_MAP.put('作', "WTHF");
        WUBI_MAP.put('节', "ABJ");
        WUBI_MAP.put('奏', "DWGJ");
        WUBI_MAP.put('过', "FPI");
        WUBI_MAP.put('快', "NNB");
        WUBI_MAP.put('慢', "NJAN");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('当', "IVF");
        WUBI_MAP.put('班', "GYNA");
        WUBI_MAP.put('制', "RJH");
        WUBI_MAP.put('夜', "YOD");
        WUBI_MAP.put('班', "GYNA");
        WUBI_MAP.put('倒', "WGI");
        WUBI_MAP.put('班', "GYNA");
        WUBI_MAP.put('加', "LKG");
        WUBI_MAP.put('班', "GYNA");
        WUBI_MAP.put('超', "FHO");
        WUBI_MAP.put('时', "JFY");
        WUBI_MAP.put('工', "AAA");
        WUBI_MAP.put('作', "WTHF");
        WUBI_MAP.put('休', "WTO");
        WUBI_MAP.put('息', "THD");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('足', "KHU");
        WUBI_MAP.put('睡', "HFTJ");
        WUBI_MAP.put('眠', "HNE");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('足', "KHU");
        WUBI_MAP.put('营', "APNN");
        WUBI_MAP.put('养', "UDYJ");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('良', "YVE");
        WUBI_MAP.put('个', "WH");
        WUBI_MAP.put('人', "WW");
        WUBI_MAP.put('防', "BLH");
        WUBI_MAP.put('护', "RYL");
        WUBI_MAP.put('用', "ET");
        WUBI_MAP.put('品', "KGU");
        WUBI_MAP.put('使', "WVT");
        WUBI_MAP.put('用', "ET");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('当', "IVF");
        WUBI_MAP.put('缺', "RMWY");
        WUBI_MAP.put('乏', "TPI");
        WUBI_MAP.put('维', "XWY");
        WUBI_MAP.put('护', "RYL");
        WUBI_MAP.put('保', "WJS");
        WUBI_MAP.put('养', "UDYJ");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('及', "EYI");
        WUBI_MAP.put('时', "JFY");
        WUBI_MAP.put('更', "GEJ");
        WUBI_MAP.put('换', "RQM");
        WUBI_MAP.put('损', "RJG");
        WUBI_MAP.put('坏', "FGNN");
        WUBI_MAP.put('失', "RWI");
        WUBI_MAP.put('效', "YQU");
        WUBI_MAP.put('安', "PVF");
        WUBI_MAP.put('全', "WGF");
        WUBI_MAP.put('意', "UYN");
        WUBI_MAP.put('识', "YKW");
        WUBI_MAP.put('淡', "IYO");
        WUBI_MAP.put('薄', "AIGF");
        WUBI_MAP.put('违', "TFPD");
        WUBI_MAP.put('章', "UJJ");
        WUBI_MAP.put('操', "RJGH");
        WUBI_MAP.put('作', "WTHF");
        WUBI_MAP.put('冒', "JHF");
        WUBI_MAP.put('险', "WGI");
        WUBI_MAP.put('作', "WTHF");
        WUBI_MAP.put('业', "GKD");
        WUBI_MAP.put('培', "RJGF");
        WUBI_MAP.put('训', "YFH");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('足', "KHU");
        WUBI_MAP.put('缺', "RMWY");
        WUBI_MAP.put('乏', "TPI");
        WUBI_MAP.put('应', "YIU");
        WUBI_MAP.put('急', "QVN");
        WUBI_MAP.put('处', "THD");
        WUBI_MAP.put('理', "GL");
        WUBI_MAP.put('能', "CEX");
        WUBI_MAP.put('力', "LT");
        WUBI_MAP.put('健', "WVG");
        WUBI_MAP.put('康', "YVL");
        WUBI_MAP.put('监', "JTL");
        WUBI_MAP.put('护', "RYL");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('到', "GCF");
        WUBI_MAP.put('位', "WUG");
        WUBI_MAP.put('定', "PGD");
        WUBI_MAP.put('期', "ADW");
        WUBI_MAP.put('体', "WSG");
        WUBI_MAP.put('检', "SRH");
        WUBI_MAP.put('缺', "RMWY");
        WUBI_MAP.put('失', "RWI");
        WUBI_MAP.put('职', "BKG");
        WUBI_MAP.put('业', "GKD");
        WUBI_MAP.put('病', "UTE");
        WUBI_MAP.put('防', "BLH");
        WUBI_MAP.put('治', "IFH");
        WUBI_MAP.put('知', "TDG");
        WUBI_MAP.put('识', "YKW");
        WUBI_MAP.put('缺', "RMWY");
        WUBI_MAP.put('乏', "TPI");
        WUBI_MAP.put('自', "THD");
        WUBI_MAP.put('我', "TRN");
        WUBI_MAP.put('保', "WJS");
        WUBI_MAP.put('护', "RYL");
        WUBI_MAP.put('意', "UYN");
        WUBI_MAP.put('识', "YKW");
        WUBI_MAP.put('不', "GI");
        WUBI_MAP.put('强', "JLXY");
        
        // 添加更多常用汉字的五笔编码...
        // 注意：这里只是示例，实际项目中应该使用完整的五笔编码数据库
    }

    /**
     * 检查字符串是否包含中文
     */
    public static boolean containsChinese(String str) {
        return PinyinUtil.containsChinese(str);
    }
}
